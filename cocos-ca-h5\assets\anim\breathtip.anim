{"__type__": "cc.AnimationClip", "_name": "breathtip", "_objFlags": 0, "_native": "", "_duration": 0.26666666666666666, "sample": 30, "speed": 0.1, "wrapMode": 2, "curveData": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.03333333333333333, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 0.06666666666666667, "value": {"__type__": "cc.Vec2", "x": 0.8, "y": 0.8}}, {"frame": 0.1, "value": {"__type__": "cc.Vec2", "x": 0.7, "y": 0.7}}, {"frame": 0.13333333333333333, "value": {"__type__": "cc.Vec2", "x": 0.6, "y": 0.6}}, {"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec2", "x": 0.7, "y": 0.7}}, {"frame": 0.2, "value": {"__type__": "cc.Vec2", "x": 0.8, "y": 0.8}}, {"frame": 0.23333333333333334, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 0.26666666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "events": []}