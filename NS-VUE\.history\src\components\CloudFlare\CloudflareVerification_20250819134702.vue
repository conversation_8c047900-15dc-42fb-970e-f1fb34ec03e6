<template>
  <!-- CloudFlare 验证对话框 -->
  <div v-if="showVerifyDialog" class="cloudflare-verify-overlay" @click="handleOverlayClick">
    <div class="verify-dialog" @click.stop>
      <div class="verify-header">
        <h3>安全验证</h3>
        <button @click="closeVerify" class="close-btn">×</button>
      </div>
      <div class="verify-content">
        <iframe
          ref="verifyFrame"
          :src="verifyUrl"
          class="verify-iframe"
          frameborder="0"
          @load="onFrameLoad"
          @error="onFrameError"
        ></iframe>
      </div>
      <div v-if="showError" class="error-message">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import type { CloudFlareScene, VerifyResult, CloudflareConfig } from "./types";
import { useCloudflareVerification } from "./useCloudflareVerification";

// Props 定义
interface Props {
  config?: CloudflareConfig;
  enableAutoClose?: boolean;
}

// Emits 定义
interface Emits {
  (e: "success", result: VerifyResult): void;
  (e: "cancel"): void;
  (e: "error", error: Error): void;
}

const props = withDefaults(defineProps<Props>(), {
  enableAutoClose: false,
});

const emit = defineEmits<Emits>();

// CloudFlare 验证实例
const cloudflareVerification = useCloudflareVerification(props.config);

// 解构状态和方法
const {
  showVerifyDialog,
  verifyUrl,
  getVerifyToken,
  closeVerify,
  onVerifySuccess,
  onVerifyFailed,
} = cloudflareVerification;

// 本地状态
const verifyFrame = ref<HTMLIFrameElement>();
const showError = ref(false);
const errorMessage = ref("");

// 方法
const handleOverlayClick = () => {
  // CloudFlare 验证对话框背景点击
  if (props.enableAutoClose) {
    closeVerify();
  }
};

const onFrameLoad = () => {
  console.log("CloudFlare 验证框架加载完成");
};

const onFrameError = () => {
  console.error("CloudFlare 验证框架加载失败");
  showError.value = true;
  errorMessage.value = "验证服务加载失败，请刷新重试";
};

// 公开方法
const startCloudflareVerification = async (scene: CloudFlareScene) => {
  try {
    const result = await getVerifyToken(scene);
    emit("success", result);
    return result;
  } catch (error) {
    console.error("CloudFlare 验证失败:", error);
    emit("error", error as Error);
    throw error;
  }
};

// 暴露方法
defineExpose({
  startCloudflareVerification,
  closeVerify,
});
</script>

<style scoped>
/* CloudFlare 验证对话框样式 */
.cloudflare-verify-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.verify-dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.verify-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.verify-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

.verify-content {
  padding: 24px;
  min-height: 300px;
}

.verify-iframe {
  width: 100%;
  height: 300px;
  border: none;
  border-radius: 8px;
}

.error-message {
  padding: 16px 24px;
  background-color: #fee;
  color: #c33;
  border-top: 1px solid #eee;
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .verify-dialog {
    width: 95%;
    margin: 20px;
  }

  .verify-content {
    padding: 20px;
  }

  .verify-header {
    padding: 16px 20px;
  }
}
</style>
