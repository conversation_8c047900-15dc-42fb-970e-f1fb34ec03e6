{"__type__": "cc.AnimationClip", "_name": "roomlist2", "_objFlags": 0, "_native": "", "_duration": 0.4166666666666667, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"roombg": {"props": {"y": [{"frame": 0, "value": 0, "curve": "quartIn"}, {"frame": 0.3333333333333333, "value": 1300}]}}, "lay/btnScroll": {"props": {"x": [{"frame": 0, "value": 0, "curve": [0.6, -0.27, 0.73, 0.04]}, {"frame": 0.3333333333333333, "value": 2500}]}}, "lay/downNd": {"props": {"y": [{"frame": 0.16666666666666666, "value": -455}, {"frame": 0.4166666666666667, "value": -790}]}}, "lay/lanbg": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.3333333333333333, "value": 0}]}}, "lay/GameListTab": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.03333333333333333, "value": 0}]}}, "lay/title": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.4166666666666667, "value": 0}]}}}}, "events": []}