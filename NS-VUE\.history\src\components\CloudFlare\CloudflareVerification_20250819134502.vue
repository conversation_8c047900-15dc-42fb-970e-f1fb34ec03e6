<template>
  <!-- CloudFlare 验证对话框 -->
  <div v-if="showVerifyDialog" class="cloudflare-verify-overlay" @click="handleOverlayClick">
    <div class="verify-dialog" @click.stop>
      <div class="verify-header">
        <h3>安全验证</h3>
        <button @click="closeVerify" class="close-btn">×</button>
      </div>
      <div class="verify-content">
        <iframe
          ref="verifyFrame"
          :src="verifyUrl"
          class="verify-iframe"
          frameborder="0"
          @load="onFrameLoad"
          @error="onFrameError"
        ></iframe>
      </div>
      <div v-if="showError" class="error-message">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>


</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import type {
  CloudFlareScene,
  VerifyType,
  CloudflareVerificationProps,
  CloudflareVerificationEmits,
} from "./types";
import { DEFAULT_SCENE_CONFIG } from "./types";
import { useCloudflareVerification, usePhoneVerification } from "./useCloudflareVerification";
import { PhoneValidator, DeviceUtils } from "./utils";

/**
 * 简单的通知函数
 * @param message 消息内容
 */
const showNotification = (message: string) => {
  try {
    import("vant")
      .then(({ showToast }) => {
        showToast(message);
      })
      .catch(() => {
        console.log("📢 通知:", message);
      });
  } catch {
    console.log("📢 通知:", message);
  }
};

// Props 和 Emits
const props = withDefaults(defineProps<CloudflareVerificationProps>(), {
  enableAutoClose: false,
  countdownDuration: 60,
});

const emit = defineEmits<CloudflareVerificationEmits>();

// Composables
const cloudflareVerification = useCloudflareVerification(props.config);
const phoneVerification = usePhoneVerification();

// 解构状态和方法
const {
  showVerifyDialog,
  verifyUrl,
  getVerifyToken,
  closeVerify,
  onVerifySuccess,
  onVerifyFailed,
} = cloudflareVerification;

const {
  verificationState,
  showVerification,
  closeVerification,
  clearErrors,
  validatePhone,
  sendVerificationCode,
  verifyCode,
} = phoneVerification;

// 本地状态
const verifyFrame = ref<HTMLIFrameElement>();
const showError = ref(false);
const errorMessage = ref("");

// 计算属性
const currentTitle = computed(() => {
  const config = DEFAULT_SCENE_CONFIG[props.verifyType];
  return config?.title || "手机验证";
});

// 方法
const formatPhoneNumber = (phone: string) => {
  return PhoneValidator.formatPhoneDisplay(phone);
};

const clearPhoneError = () => {
  verificationState.phoneError = false;
  verificationState.phoneErrorMessage = "";
};

const clearCodeError = () => {
  verificationState.codeError = false;
  verificationState.codeErrorMessage = "";
};

const onInputFocus = () => {
  if (DeviceUtils.needScreenUp()) {
    document.body.style.transform = "translateY(-100px)";
  }
};

const onInputBlur = () => {
  document.body.style.transform = "";
};

const proceedToCodeStep = async () => {
  if (!validatePhone()) return;

  try {
    await sendVerificationCode(props.verifyType, cloudflareVerification);
  } catch (error) {
    console.error("发送验证码失败:", error);
    showNotification("发送验证码失败");
  }
};

const resendCode = async () => {
  try {
    await sendVerificationCode(props.verifyType, cloudflareVerification);
  } catch (error) {
    console.error("重新发送验证码失败:", error);
    showNotification("重新发送失败");
  }
};

const submitVerification = async () => {
  try {
    const result = await verifyCode(props.verifyType);

    if (result.success) {
      emit("success", result);
      closePhoneVerification();
      showNotification("验证成功");
    } else {
      emit("error", new Error(result.error || "验证失败"));
    }
  } catch (error) {
    console.error("提交验证失败:", error);
    emit("error", error as Error);
  }
};

const closePhoneVerification = () => {
  closeVerification();
  emit("cancel");
};

const handleOverlayClick = () => {
  // CloudFlare 验证对话框背景点击
  if (props.enableAutoClose) {
    closeVerify();
  }
};

const handlePhoneOverlayClick = () => {
  // 手机验证对话框背景点击
  closePhoneVerification();
};

const onFrameLoad = () => {
  console.log("CloudFlare 验证框架加载完成");
};

const onFrameError = () => {
  console.error("CloudFlare 验证框架加载失败");
  showError.value = true;
  errorMessage.value = "验证服务加载失败，请刷新重试";
};

// 公开方法
const startVerification = (verifyType?: VerifyType, initialPhone?: string) => {
  const type = verifyType || props.verifyType;
  showVerification(type, initialPhone || props.initialPhone);
};

const startCloudflareVerification = async (scene: CloudFlareScene) => {
  try {
    const result = await getVerifyToken(scene);
    return result;
  } catch (error) {
    console.error("CloudFlare 验证失败:", error);
    throw error;
  }
};

// 生命周期
onMounted(() => {
  // 如果有初始验证类型，自动显示
  if (props.verifyType !== undefined) {
    startVerification();
  }
});

onUnmounted(() => {
  document.body.style.transform = "";
});

// 暴露方法
defineExpose({
  startVerification,
  startCloudflareVerification,
  closeVerification: closePhoneVerification,
  closeCloudflareVerification: closeVerify,
});
</script>

<style scoped>
/* CloudFlare 验证对话框样式 */
.cloudflare-verify-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.verify-dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

.verify-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.verify-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.verify-content {
  padding: 24px;
  min-height: 300px;
}

.verify-iframe {
  width: 100%;
  height: 300px;
  border: none;
  border-radius: 8px;
}

.error-message {
  padding: 16px 24px;
  background-color: #fee;
  color: #c33;
  border-top: 1px solid #eee;
  text-align: center;
}

/* 手机验证对话框样式 */
.phone-verification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.verification-dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

.dialog-content {
  padding: 24px;
}

.phone-input-step,
.code-input-step {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.phone-display {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 8px;
}

.phone-display span {
  color: #666;
  font-size: 14px;
}

.phone-display strong {
  color: #333;
  font-size: 16px;
  margin-left: 8px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-group label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.input-wrapper {
  position: relative;
}

.phone-input,
.code-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s;
  box-sizing: border-box;
}

.phone-input:focus,
.code-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.phone-input.error,
.code-input.error {
  border-color: #dc3545;
}

.error-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #dc3545;
  border-radius: 0 0 8px 8px;
}

.error-text {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
}

.code-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 40px;
}

.countdown {
  color: #666;
  font-size: 14px;
}

.primary-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
}

.primary-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.primary-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondary-btn {
  background: transparent;
  color: #007bff;
  border: 2px solid #007bff;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.secondary-btn:hover:not(:disabled) {
  background: #007bff;
  color: white;
}

.secondary-btn:disabled {
  border-color: #ccc;
  color: #ccc;
  cursor: not-allowed;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .verification-dialog,
  .verify-dialog {
    width: 95%;
    margin: 20px;
  }

  .dialog-content,
  .verify-content {
    padding: 20px;
  }

  .dialog-header,
  .verify-header {
    padding: 16px 20px;
  }

  .phone-input,
  .code-input {
    font-size: 16px; /* 防止 iOS 缩放 */
  }
}
</style>
