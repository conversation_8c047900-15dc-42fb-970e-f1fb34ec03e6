import CloudflareCtrl, { CloudFlareScene } from "./Cloudflare/CloudflareCtrl";
import { DEEP_INDEXZ, UI_PATH_DIC } from "./GlobalConstant";
import Global from "./GlobalScript";
import UICommon from "./component/UICommon";
import { GEETEST_TYPE, GeetestMgr } from "./geetest/GeetestMgr";
import { Md5 } from "./libs/Md5";
import { uiManager } from "./mgr/UIManager";
import HttpUtils from "./net/HttpUtils";
import utils from "./utils/utils";

export function showLoginPassword(phone?: string, mcode?: string, cb?, panelzIndex?: any) {
    uiManager.instance.showDialog(UI_PATH_DIC.SetLoginPassword, [{ phone: phone, mcode: mcode }], null, panelzIndex || DEEP_INDEXZ.VERIFY_CODE);
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class LoginPassword extends UICommon {
    //button label continue
    @property(cc.Label)
    lb_continue: cc.Label = null;

    //副标题
    @property(cc.Label)
    lb_fu: cc.Label = null;

    @property(cc.Label)
    lbError1: cc.Label = null;

    @property(cc.Node)
    ndRed1: cc.Node = null

    @property(cc.Label)
    lbError2: cc.Label = null;

    @property(cc.Node)
    ndRed2: cc.Node = null

    @property(cc.EditBox)
    password_edit1: cc.EditBox = null;

    @property(cc.EditBox)
    password_edit2: cc.EditBox = null;

    //眼的button node
    @property(cc.Node)
    eyeNode: cc.Node = null

    //用来适配原生输入框
    @property(cc.Node)
    uiBox: cc.Node = null

    m_code: string = '';//带过来的验证码 如果有的话

    m_password: string = "";
    m_passwordRe: string = "";
    // m_code: string = "";
    m_t: number = 120;

    bindType: string = '';

    m_phone: string = null

    onLoad(): void {
        this.password_edit1.node.active = true;
        this.password_edit2.node.active = false;
        this.m_password = ""
        this.m_passwordRe = ""
    }

    init(args: { phone?: string, mcode?: string }) {
        this.m_phone = args.phone;
        this.m_code = args.mcode || '';
        // let mainNode = this.node.getChildByName("UiBox");
        // mainNode.position = new cc.Vec3(0,-1200);
        //cc.tween(mainNode).to(0.1,{position:new cc.Vec3(0,-600)}).start();
    }

    onPasswordText1(text, editbox, customEventData) {
        this.m_password = text;
    }

    onPasswordText2(text, editbox, customEventData) {
        this.m_passwordRe = text;
    }

    close() {
        // this.hide()
        let mainNode = this.node.getChildByName("UiBox");
        cc.tween(mainNode).to(0.1, { position: new cc.Vec3(0, -1200) }).call(() => {
            this.node.destroy();
        }).start();
    }
    //点击切换密码 是否可见
    passwordIsShowClick(event) {
        let eye = this.eyeNode.getChildByName('eye')
        let noeye = this.eyeNode.getChildByName('noeye')
        noeye.active = !noeye.active;
        eye.active = !noeye.active;

        if (noeye.active) {
            if (this.password_edit1.node.active) {
                this.password_edit1.inputFlag = cc.EditBox.InputFlag.PASSWORD;
            } else {
                this.password_edit2.inputFlag = cc.EditBox.InputFlag.PASSWORD;
            }
        } else {
            if (this.password_edit1.node.active) {
                this.password_edit1.inputFlag = cc.EditBox.InputFlag.DEFAULT;
            } else {
                this.password_edit2.inputFlag = cc.EditBox.InputFlag.DEFAULT;
            }
        }
    }

    onEdittingDidBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox, this.uiBox, 800)
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""
    }

    onEdittingDidEnd(editbox: cc.EditBox) {
        if (Global.getInstance().needScreenUp()) {
            const widght = this.uiBox.getComponent(cc.Widget)
            widght.bottom = 34;
            widght.updateAlignment();
        }
        editbox.placeholder = editbox.placeholderLabel.node.name
    }

    onEdittingCodeDidBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox, this.uiBox, 800)
        this.lbError1.string = ""
        this.lbError2.string = ""
        this.ndRed1.active = false
        this.ndRed2.active = false
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""
    }

    onEdittingCodeDidEnd(editbox: cc.EditBox) {
        if (Global.getInstance().needScreenUp()) {
            const widght = this.uiBox.getComponent(cc.Widget)
            widght.bottom = 34;
            widght.updateAlignment();
        }
        editbox.placeholder = editbox.placeholderLabel.node.name
    }
    continueClick() {
        if (this.m_password == "") {
            this.lbError1.string = "Password can not be empty"
            this.ndRed1.active = true
            return;
        }
        if (this.m_password.length < 8 || this.m_password.length > 20 || !utils.hasNumberAndCharacter(this.m_password)) {
            this.lbError1.string = "The login password is 8~20 characters and must\ncontain letters and numbers."
            this.ndRed1.active = true
            return;
        }
        this.password_edit1.node.active = false;
        this.password_edit2.node.active = true;
        this.lb_fu.string = "Re-enter the same password";
        let noeye = this.eyeNode.getChildByName('noeye')
        let eye = this.eyeNode.getChildByName('eye')
        eye.active = true;
        noeye.active = false;
        this.password_edit2.inputFlag = cc.EditBox.InputFlag.DEFAULT;
        this.lb_continue.string = "Confirm"
        this.lbError1.string = ""
        this.ndRed1.active = false
    }
    confirmClick(event, data) {
        if (this.password_edit1.node.active) {
            this.continueClick();
            return;
        }
        if (this.m_passwordRe == "") {
            // Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword25"));
            this.lbError2.string = "Password can not be empty"
            this.ndRed2.active = true
            return;
        }
        if (this.m_password != this.m_passwordRe) {
            // this.lbError1.string = "The passwords entered twice are inconsistent"
            this.lbError2.string = "The passwords entered twice are inconsistent"
            this.ndRed2.active = true
            return;
        }


        if (this.m_passwordRe.length < 8 || this.m_passwordRe.length > 20 || !utils.hasNumberAndCharacter(this.m_passwordRe)) {
            // Global.getInstance().showSimpleTip("Please enter at least 8 characters");
            this.lbError2.string = "The login password is 8~20 characters and must contain letters and numbers."
            this.ndRed2.active = true
            return
        }
        this.lbError2.string = ""
        this.ndRed2.active = false

        let phone = this.m_phone;
        let obj = { "phone": phone, "password": this.m_password }

        let geeid = GEETEST_TYPE.first_password
        if (Global.getInstance()?.userdata?.login_password) {
            geeid = GEETEST_TYPE.forget_password
        }
        if (!Global.getInstance().token) {
            geeid = GEETEST_TYPE.forget_password
        }
        //加个验证
        GeetestMgr.instance.geetest_device(geeid, (succ) => {
            if (succ) {
                let ret = {}
                if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
                    ret = succ
                }
                this.resetPassword(obj, ret);
            }
        })

        // if (Global.getInstance().loginVerifyType === 1) {
        //     let geeid = GEETEST_TYPE.first_password
        //     if (Global.getInstance()?.userdata?.login_password) {
        //         geeid = GEETEST_TYPE.forget_password
        //     }
        //     if (!Global.getInstance().token) {
        //         geeid = GEETEST_TYPE.forget_password
        //     }
        //     //加个验证
        //     GeetestMgr.instance.geetest_device(geeid, (succ) => {
        //         if (succ) {
        //             let ret = {}
        //             if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
        //                 ret = succ
        //             }
        //             this.resetPassword(obj, ret);
        //         }
        //     })
        // } else {
        //     CloudflareCtrl.instance.getVerifyToken(CloudFlareScene.FORGET_PW_SUBMIT, res => {
        //         if (res.code === 0 && res.token) {
        //             this.resetPassword(obj);
        //         }
        //     });
        // }

    }

    resetPassword(data, ret?) {
        let isForgetPw = Global.getInstance()?.userdata?.login_password;
        if (!Global.getInstance().token) {
            isForgetPw = true;//这里有个bug 如果没有设置登陆密码的时候
            // 没有登陆的时候 点击forget
        }
        let params = {
            token: Global.getInstance().token || '',
            "phone": this.m_phone ? this.m_phone : data.phone,
            "telephone_code": "+63",
            "verify_code": this.m_code,//同时要验证验证码 bug
            upd_column: isForgetPw ? "forget_password" : 'password',
            "password": Md5.hashStr(data.password).toString(),
            buds: '64',
        };
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        params['userInfo'] = uInfo;
        params['geetest_guard'] = gee_guard;
        params['geetest_captcha'] = gee_captcha;
        params['buds'] = ret?.buds;
        // if (Global.instance.loginVerifyType == 1) {
        //     let gee_guard = ret?.geetest_guard || '';
        //     let uInfo = ret?.userInfo || '';
        //     let gee_captcha = ret?.geetest_captcha || '';
        //     params['userInfo'] = uInfo;
        //     params['geetest_guard'] = gee_guard;
        //     params['geetest_captcha'] = gee_captcha;
        //     params['buds'] = ret?.buds;
        // } else {
        //     //加上CF 参数
        //     params["cf-token"] = CloudflareCtrl.instance.lastToken;
        //     params["cf-scene"] = CloudflareCtrl.instance.lastScene;
        // }
        HttpUtils.getInstance().post(3, 3, this, "/common/api/set/phone/passwd", params, (response) => {
            this.hide()
            if (Global.getInstance().userdata) {
                Global.getInstance().userdata.login_password = 1
            }
            Global.getInstance().showSimpleTip("Login password set successfully")
            cc.director.emit("setLoginPwd_suc", { phone: data.phone, password: data.password })
        }, (response) => {
            if (response && response.code && response.msg) {
                cc.log("response.code ===>", response.code)
                Global.getInstance().showSimpleTip(response?.msg);
                this.hide()
            }
            cc.log("response ===>", JSON.stringify(response))
        });
    }

    protected onDestroy(): void {
    }
}
