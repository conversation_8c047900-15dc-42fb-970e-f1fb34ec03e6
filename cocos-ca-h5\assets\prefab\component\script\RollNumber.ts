/*
 * @Copyright: Copyright (c) 2019
 * @Author: 一枚小工
 * @Version: 1.0
 * @Date: 2019-11-13 15:00:30
 */

const {ccclass, property} = cc._decorator;

@ccclass
export default class NewClass extends cc.Component {
    // 固定的位数，0表示有几位显示几位
    @property
    fixed: number = 1;

    // 每一位的宽度
    @property
    width: number = 10;

    // 动画速率
    @property
    speed: number = 0.2;

    // 数字纹理0-9
    @property([cc.SpriteFrame])
    numberSpriteFrame: cc.SpriteFrame[] = [];    

    // 数字布局
    private numLayout: cc.Layout = null;
    // 数字字符串
    private numText: string = '';  
    // 显示数字节点
    private numNodes: cc.Node[] = [];
    // 节点池
    private nodePool: cc.NodePool = null;

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        this.init();
    }

    init():void{
        this.numText = '0';
        this.nodePool = new cc.NodePool('RollNumber');
        this.numLayout = this.getComponent(cc.Layout);
    }

    start () {
        this.setValue(0, false);
    }

    // update (dt) {}

    /**
     * @description: 设置数值
     * @param {num} 数值
     * @param {animate} 是否有动画
     * @return: 
     */
    setValue(num: number, animate: boolean){
        this.checkNumNodes(num);
        
        // 修改数值
        let length = this.numNodes.length;
        for(let i = 0; i < length; i++){
            let index:number = parseInt(this.numText[length - 1 - i]);
            this.setNodeNum(this.numNodes[i], index, animate);
        }
    }

    /**
     * @description: 检测节点数和数字位数
     * @param {num} 数值
     * @return: 
     */
    checkNumNodes(num:number):void{
        let numText:string = num.toString();

        // 固定长度前面补0
        if(this.fixed > 0 && this.fixed > numText.length){
            while(numText.length < this.fixed){
                numText = '0' + numText;
            }
        }

        let offset:number = numText.length - this.numNodes.length;
        // 增加节点
        if(offset > 0){
            for(let i = 0; i < offset; i++){
                let node = this.createNumNode(); 
                // 从前面加               
                this.numNodes.unshift(node);               
            }
        }
        // 删除节点
        else if(offset < 0){
            offset = -offset;
            for(let i = 0; i < offset; i++){
                // 从前面删
                let node = this.numNodes.shift();
                this.recycleNumNode(node);
            }
        }
        this.numLayout.updateLayout();
        this.numText = numText;
    }

    /**
     * @description: 根据索引生成数字节点
     * @return: 生成的数字1位节点
     */
    createNumNode(): cc.Node{
        let node: cc.Node = null;

        if(this.nodePool.size() > 0){
            node = this.nodePool.get();
        }else{
            // 每一位数字节点
            node = new cc.Node('numnode');
            node.anchorY = 1;
            node.width = this.width;
            node.parent = this.numLayout.node;
            
            // 容器设置
            let layout: cc.Layout = node.addComponent(cc.Layout);
            layout.spacingY = 5;
            layout.paddingTop = 5;
            layout.paddingBottom = 5;
            layout.type = cc.Layout.Type.VERTICAL;
            layout.resizeMode = cc.Layout.ResizeMode.CONTAINER;
            layout.horizontalDirection = cc.Layout.HorizontalDirection.LEFT_TO_RIGHT;          
            
            // 添加数字列表 0-9
            for(let i = 0; i <= 10; i++){
                let numnode = new cc.Node(i.toString());

                // 数字纹理设置
                let sprite = numnode.addComponent(cc.Sprite);
                sprite.spriteFrame = this.numberSpriteFrame[i % 10];
                sprite.type = cc.Sprite.Type.SIMPLE;
                sprite.sizeMode = cc.Sprite.SizeMode.RAW;
                sprite.trim = false;                
                numnode.parent = node;
            }
            layout.updateLayout();
        }
        
        return node;
    }

    /**
     * @description: 回收数字节点 
     * @param {node} 待回收节点
     * @return: 
     */
    recycleNumNode(node: cc.Node): void{
        this.nodePool.put(node);
    }

    /**
     * @description: 设置节点上的数字
     * @param {node} 目标节点
     * @param {num} 数字值
     * @param {animate} 是否有动画
     * @return: 
     */
    setNodeNum(node: cc.Node, num: number, animate: boolean):void{
        if(num < 0 || num > 9){
            return;
        }
        
        node.stopAllActions();

        let targetY:number = node.getChildByName(num.toString()).y;
        
        if(!animate){
            node.y = -targetY;
            return;
        }

        let currentY:number = node.y;        
        let minY:number = node.getChildByName('10').y;
        let maxY:number = node.getChildByName('0').y;

        let offset: number = -targetY;
        if(currentY < offset){
            node.runAction(cc.sequence(
                cc.moveTo(this.calcMoveTime(offset, maxY - minY), cc.v2(0, offset)),
                cc.callFunc( () => {
                })
            ));
        }
        else if(currentY > offset){
            node.runAction(cc.sequence(
                cc.moveTo(this.calcMoveTime(currentY + minY, maxY - minY), 0, -minY),
                cc.moveTo(0, 0, -maxY),
                cc.moveTo(this.calcMoveTime(offset, maxY - minY), 0, offset),
                cc.callFunc( () => {
                })
            ));
        }
    }

    calcMoveTime(offset:number, max: number):number{
        if(max == 0){
            max = 1;
        }
        return Math.abs(offset) / Math.abs(max) * this.speed;
    }
}
